import 'package:flutter/material.dart';
import '../services/bluetooth_service.dart';
import '../services/project_service.dart';
import '../services/command_service.dart';
import '../services/offline_mode_service.dart';
import '../services/location_service.dart';
import '../services/welding_joint_number_service.dart';
import '../models/project_model.dart';
import '../models/offline_data_model.dart';
import 'dart:async';

class WeldingConfigScreen extends StatefulWidget {
  @override
  _WeldingConfigScreenState createState() => _WeldingConfigScreenState();
}

class _WeldingConfigScreenState extends State<WeldingConfigScreen> {
  final BleService _bleService = BleService();
  final ProjectService _projectService = ProjectService();
  final CommandService _commandService = CommandService();
  final OfflineModeService _offlineModeService = OfflineModeService();

  Project? _currentProject;
  bool _isLoading = true;
  bool _isSending = false;
  String _machineNumber = '';
  String _statusMessage = '';

  // 添加设备信息相关变量
  Map<String, String> _deviceInfo = {
    'connectionStatus': '未知',
    'machineNumber': '未知',
    'weldingStandard': '未知',
    'machineType': '未知',
    'cylinderArea': '未知',
  };
  bool _isLoadingDeviceInfo = false;

  // 添加焊机数据相关变量
  String _weldingData = '';
  bool _isLoadingWeldingData = false;
  Map<String, dynamic>? _parsedWeldingData;

  // 添加GPS定位相关变量
  String _locationData = '';
  bool _isLoadingLocation = false;

  // 离线模式相关变量
  OfflineModeState _offlineState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );

  StreamSubscription? _receivedDataSubscription;
  StreamSubscription? _offlineStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadCurrentProject();
    _checkConnectionAndSendInitialCommand();

    // 延迟读取设备信息，确保蓝牙连接稳定
    if (_bleService.isConnected) {
      // 延迟3秒后读取设备信息，让连接充分稳定
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _bleService.isConnected) {
          _loadDeviceInfo();
        }
      });
    }

    _receivedDataSubscription = _bleService.receivedDataStream.listen((data) {
      if (data.startsWith('【重要】焊机编号:') ||
          data.startsWith('焊机编号:') ||
          data.startsWith('【重要】设备焊机编号(ASCII文本):')) {
        String machineNumber = data.split(':').last.trim();
        setState(() {
          _machineNumber = machineNumber;
          _deviceInfo['machineNumber'] = machineNumber;
          _statusMessage = '已获取到焊机编号: $machineNumber';
        });
      } else if (data.startsWith('【重要】焊接标准:') || data.startsWith('焊接标准:')) {
        String standard = data.split(':').last.trim();
        setState(() {
          _deviceInfo['weldingStandard'] = standard;
          _statusMessage = '已获取到焊接标准';
          print('焊接标准已更新: $standard'); // 调试用
        });
      } else if (data.startsWith('【重要】焊机机型:') || data.startsWith('焊机机型:')) {
        String type = data.split(':').last.trim();
        setState(() {
          _deviceInfo['machineType'] = type;
          print('焊机机型已更新: $type'); // 调试用
        });
      } else if (data.startsWith('【重要】油缸面积:') || data.startsWith('油缸面积:')) {
        String area = data.split(':').last.trim();
        setState(() {
          _deviceInfo['cylinderArea'] = area;
          print('油缸面积已更新: $area'); // 调试用
        });
      } else if (data.startsWith('【重要】连接状态:') || data.startsWith('连接状态:')) {
        String status = data.split(':').last.trim();
        setState(() {
          _deviceInfo['connectionStatus'] = status;
          print('连接状态已更新: $status'); // 调试用
        });
      } else if (data.startsWith('【重要】160字节焊机数据已接收')) {
        setState(() {
          _statusMessage = '160字节焊机数据已接收';
        });
      } else if (data.startsWith('160字节焊机数据:')) {
        // 修复数据解析问题：正确提取数据内容并去除前后空白字符
        String weldingDataContent = data.substring('160字节焊机数据:'.length).trim();
        setState(() {
          _weldingData = weldingDataContent;
          _parsedWeldingData = _parseWeldingData(weldingDataContent);
          _isLoadingWeldingData = false;
          _statusMessage = '160字节焊机数据获取成功';
        });
        print(
            '160字节焊机数据已更新: ${weldingDataContent.length > 50 ? weldingDataContent.substring(0, 50) + '...' : weldingDataContent}'); // 调试用，只显示前50个字符
      }
    });
  }

  // 初始化服务
  Future<void> _initializeServices() async {
    try {
      await _offlineModeService.initialize();

      // 获取当前状态
      setState(() {
        _offlineState = _offlineModeService.currentState;
      });

      // 监听离线模式状态变化
      _offlineStateSubscription =
          _offlineModeService.stateStream.listen((state) {
        setState(() {
          _offlineState = state;
        });
      });

      // 设置当前项目和用户（这里需要根据实际情况获取用户ID）
      if (_currentProject != null) {
        await _offlineModeService.setCurrentUserAndProject(
          'user_001', // 这里应该从用户登录状态获取
          _currentProject!.id,
        );
      }
    } catch (e) {
      print('初始化离线模式服务失败: $e');
    }
  }

  @override
  void dispose() {
    _receivedDataSubscription?.cancel();
    _offlineStateSubscription?.cancel();
    super.dispose();
  }

  // 加载当前项目
  Future<void> _loadCurrentProject() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载项目信息...';
    });

    try {
      final currentProject = await _projectService.getCurrentProject();
      setState(() {
        _currentProject = currentProject;
        _isLoading = false;
        _statusMessage = currentProject != null
            ? '项目信息已加载: ${currentProject.name}'
            : '未找到当前项目';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = '加载项目信息失败: $e';
      });
    }
  }

  // 检查蓝牙连接并发送初始命令
  Future<void> _checkConnectionAndSendInitialCommand() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，请先连接焊机';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在与焊机通信...';
      _isSending = true;
    });

    try {
      // 发送初始命令 01 06 00 03 00 01 B8 0A
      final initialCommand = _commandService.buildInitialCommand();
      final initialResult = await _bleService.sendData(initialCommand);

      if (!initialResult) {
        setState(() {
          _statusMessage = '发送初始命令失败';
          _isSending = false;
        });
        return;
      }

      // 等待响应
      await Future.delayed(Duration(milliseconds: 500));

      // 发送查询焊机编号命令 01 03 00 05 00 05 95 C8
      final queryCommand = _commandService.buildQueryMachineNumberCommand();
      final queryResult = await _bleService.sendData(queryCommand);

      if (!queryResult) {
        setState(() {
          _statusMessage = '查询焊机编号失败';
          _isSending = false;
        });
        return;
      }

      setState(() {
        _statusMessage = '已发送查询焊机编号命令，等待响应...';
        _isSending = false;
      });

      // 等待焊机编号响应
      await Future.delayed(Duration(seconds: 1));

      // 读取完整设备信息
      await _loadDeviceInfo();

      if (_machineNumber.isEmpty && _deviceInfo['machineNumber'] == '未知') {
        await Future.delayed(Duration(seconds: 1));
        _bleService.sendSpecificCommand();
      }
    } catch (e) {
      setState(() {
        _statusMessage = '与焊机通信失败: $e';
        _isSending = false;
      });
    }
  }

  // 发送项目信息到焊机
  Future<void> _sendProjectInfoToWeldingMachine() async {
    if (_currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('没有当前项目信息')),
      );
      return;
    }

    if (!_bleService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('蓝牙未连接，请先连接焊机')),
      );
      return;
    }

    setState(() {
      _statusMessage = '正在发送项目信息到焊机...';
      _isSending = true;
    });

    try {
      final result = await _commandService
          .sendProjectInfoToWeldingMachine(_currentProject!);

      setState(() {
        _isSending = false;
        _statusMessage = result ? '项目信息发送成功' : '项目信息发送部分失败';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '项目信息发送成功' : '项目信息发送部分失败'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _isSending = false;
        _statusMessage = '发送项目信息失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('发送项目信息失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 添加读取设备信息的方法
  Future<void> _loadDeviceInfo() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法读取设备信息';
        _deviceInfo['connectionStatus'] = '未连接';
      });
      return;
    }

    setState(() {
      _isLoadingDeviceInfo = true;
      _statusMessage = '正在读取设备信息...';
    });

    try {
      print('开始读取设备信息...');

      // 直接调用蓝牙服务的统一读取方法，避免重复发送命令
      print('从蓝牙服务读取完整设备信息...');
      final deviceInfo = await _bleService.readDeviceInfoForUI();
      print('设备信息已获取: $deviceInfo');

      setState(() {
        // 合并从蓝牙服务获取的设备信息
        _deviceInfo = deviceInfo;
        _isLoadingDeviceInfo = false;
        _statusMessage = '设备信息读取成功';

        // 更新机器编号显示
        if (deviceInfo['machineNumber'] != null &&
            deviceInfo['machineNumber'] != '未知' &&
            deviceInfo['machineNumber'] != '正在读取...' &&
            deviceInfo['machineNumber'] != '未获取到') {
          _machineNumber = deviceInfo['machineNumber']!;
        }
      });

      print('最终读取到的设备信息: $deviceInfo');
    } catch (e) {
      setState(() {
        _isLoadingDeviceInfo = false;
        _statusMessage = '读取设备信息失败: $e';
      });
      print('读取设备信息出错: $e');
    }
  }

  // 添加获取160字节焊机数据的方法
  Future<void> _loadWeldingData() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法获取焊机数据';
      });
      return;
    }

    setState(() {
      _isLoadingWeldingData = true;
      _statusMessage = '正在获取160字节焊机数据...';
      _weldingData = '';
    });

    try {
      print('开始获取160字节焊机数据...');

      // 发送160字节焊机数据查询命令
      bool result = await _bleService.sendQueryWeldingDataCommand();

      if (result) {
        print('160字节焊机数据查询命令发送成功，等待响应...');
        // 等待3秒接收数据
        await Future.delayed(Duration(seconds: 3));

        // 如果3秒后还没收到数据，设置超时状态
        if (_weldingData.isEmpty && _isLoadingWeldingData) {
          setState(() {
            _isLoadingWeldingData = false;
            _statusMessage = '获取焊机数据超时，请重试';
          });
        }
      } else {
        setState(() {
          _isLoadingWeldingData = false;
          _statusMessage = '发送焊机数据查询命令失败';
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingWeldingData = false;
        _statusMessage = '获取焊机数据失败: $e';
      });
      print('获取焊机数据出错: $e');
    }
  }

  // 测试数据解析功能
  void _testParseData() {
    // 模拟160字节的测试数据（320个十六进制字符）
    String testData = '01F4' + // VW2530: 管材直径 500mm
        '000B' + // VW2532: 管材SDR 11
        '002D' + // VW2534: 管材厚度 45mm
        '0019' + // VW2536: 环境温度 25°C
        '00DC' + // VW2538: 热板温度 220°C
        '0032' + // VW2540: 拖动压力 50bar
        '0000' + // VW2542: 保留
        '003C' + // VW2544: 卷边设定压力 60bar
        '003A' + // VW2546: 卷边实际压力 58bar
        '001E' + // VW2548: 卷边设定时间 30s
        '001D' + // VW2550: 卷边实际时间 29s
        '0046' + // VW2552: 吸热设定压力 70bar
        '0045' + // VW2554: 吸热实际压力 69bar
        '0078' + // VW2556: 吸热设定时间 120s
        '0077' + // VW2558: 吸热实际时间 119s
        '0005' + // VW2560: 转换时间 5s
        '000A' + // VW2562: 增压时间 10s
        '0050' + // VW2564: 冷却设定压力 80bar
        '004F' + // VW2566: 冷却实际压力 79bar
        '012C' + // VW2568: 冷却时间 300s
        '0007' + // VW2570: 焊接状态 7(焊接完成)
        '0000' +
        '0000' +
        '0000' +
        '0000' +
        '0000' + // VW2572-VW2580: 保留
        '07E8' + // VW2582: 开始年份 2024
        '0001' + // VW2584: 开始月份 1
        '0008' + // VW2586: 开始日期 8
        '000E' + // VW2588: 开始小时 14
        '001E' + // VW2590: 开始分钟 30
        '0000' + // VW2592: 开始秒钟 0
        '0000' +
        '0000' +
        '0000' +
        '0000' +
        '0000' + // VW2594-VW2602: 保留
        '07E8' + // VW2604: 结束年份 2024
        '0001' + // VW2606: 结束月份 1
        '0008' + // VW2608: 结束日期 8
        '000E' + // VW2610: 结束小时 14
        '0023' + // VW2612: 结束分钟 35
        '0000' + // VW2614: 结束秒钟 0
        // 填充剩余字节到160字节
        '0000' * 50;

    setState(() {
      _weldingData = testData;
      _parsedWeldingData = _parseWeldingData(testData);
      _statusMessage = '测试数据解析完成';
    });
  }

  // 解析160字节焊机数据
  Map<String, dynamic> _parseWeldingData(String hexData) {
    try {
      // 移除空格并转换为字节数组
      String cleanHex = hexData.replaceAll(' ', '').toUpperCase();

      if (cleanHex.length != 320) {
        return {
          'error': '数据长度错误',
          'message': '期望320字符(160字节)，实际${cleanHex.length}字符',
          'rawData': hexData,
        };
      }

      List<int> bytes = [];
      for (int i = 0; i < cleanHex.length; i += 2) {
        bytes.add(int.parse(cleanHex.substring(i, i + 2), radix: 16));
      }

      if (bytes.length != 160) {
        return {
          'error': '字节数量错误',
          'message': '期望160字节，实际${bytes.length}字节',
          'rawData': hexData,
        };
      }

      // 解析各个参数
      Map<String, dynamic> parsed = {
        'dataInfo': {
          '数据长度': '${bytes.length} 字节',
          '数据格式': '十六进制',
          '解析时间': DateTime.now().toString().substring(0, 19),
        },
        'basicParams': _parseBasicParams(bytes),
        'processParams': _parseProcessParams(bytes),
        'statusInfo': _parseStatusInfo(bytes),
        'timeInfo': _parseTimeInfo(bytes),
        'rawDataPreview': bytes
            .take(20)
            .map((b) => b.toRadixString(16).padLeft(2, '0').toUpperCase())
            .join(' '),
      };

      return parsed;
    } catch (e) {
      return {
        'error': '解析失败',
        'message': e.toString(),
        'rawData': hexData,
      };
    }
  }

  // 解析基础参数 (VW2530-VW2540)
  Map<String, dynamic> _parseBasicParams(List<int> bytes) {
    return {
      '管材直径': '${_getWordValue(bytes, 0)} mm',
      '管材SDR': _getWordValue(bytes, 1).toString(),
      '管材厚度': '${_getWordValue(bytes, 2)} mm',
      '环境温度': '${_getWordValue(bytes, 3)} °C',
      '热板温度': '${_getWordValue(bytes, 4)} °C',
      '拖动压力': '${_getWordValue(bytes, 5)} bar',
    };
  }

  // 解析工艺参数 (VW2544-VW2568)
  Map<String, dynamic> _parseProcessParams(List<int> bytes) {
    return {
      '卷边设定压力': '${_getWordValue(bytes, 7)} bar',
      '卷边实际压力': '${_getWordValue(bytes, 8)} bar',
      '卷边设定时间': '${_getWordValue(bytes, 9)} s',
      '卷边实际时间': '${_getWordValue(bytes, 10)} s',
      '吸热设定压力': '${_getWordValue(bytes, 11)} bar',
      '吸热实际压力': '${_getWordValue(bytes, 12)} bar',
      '吸热设定时间': '${_getWordValue(bytes, 13)} s',
      '吸热实际时间': '${_getWordValue(bytes, 14)} s',
      '转换时间': '${_getWordValue(bytes, 15)} s',
      '增压时间': '${_getWordValue(bytes, 16)} s',
      '冷却设定压力': '${_getWordValue(bytes, 17)} bar',
      '冷却实际压力': '${_getWordValue(bytes, 18)} bar',
      '冷却时间': '${_getWordValue(bytes, 19)} s',
    };
  }

  // 解析状态信息 (VW2570)
  Map<String, dynamic> _parseStatusInfo(List<int> bytes) {
    int statusValue = _getWordValue(bytes, 20);
    return {
      '焊接状态码': statusValue,
      '焊接状态': _getWeldingStatusText(statusValue),
      '状态描述': _getStatusDescription(statusValue),
    };
  }

  // 解析时间信息
  Map<String, dynamic> _parseTimeInfo(List<int> bytes) {
    return {
      '熔接开始日期': _parseDate(bytes, 25, 30), // VW2580-VW2590
      '熔接开始时间': _parseTime(bytes, 35, 39), // VW2600-VW2608
      '熔接结束日期': _parseDate(bytes, 45, 50), // VW2620-VW2630
      '熔接结束时间': _parseTime(bytes, 55, 59), // VW2640-VW2648
    };
  }

  // 从字节数组获取16位字值（大端序）
  int _getWordValue(List<int> bytes, int wordIndex) {
    int byteIndex = wordIndex * 2;
    if (byteIndex + 1 < bytes.length) {
      return (bytes[byteIndex] << 8) | bytes[byteIndex + 1];
    }
    return 0;
  }

  // 解析日期
  String _parseDate(List<int> bytes, int startWordIndex, int endWordIndex) {
    try {
      int year = _getWordValue(bytes, startWordIndex);
      int month = _getWordValue(bytes, startWordIndex + 1);
      int day = _getWordValue(bytes, startWordIndex + 2);

      if (year > 1900 &&
          year < 2100 &&
          month >= 1 &&
          month <= 12 &&
          day >= 1 &&
          day <= 31) {
        return '$year-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
      }
      return '无效日期';
    } catch (e) {
      return '解析错误';
    }
  }

  // 解析时间
  String _parseTime(List<int> bytes, int startWordIndex, int endWordIndex) {
    try {
      int hour = _getWordValue(bytes, startWordIndex);
      int minute = _getWordValue(bytes, startWordIndex + 1);
      int second = _getWordValue(bytes, startWordIndex + 2);

      if (hour >= 0 &&
          hour < 24 &&
          minute >= 0 &&
          minute < 60 &&
          second >= 0 &&
          second < 60) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
      }
      return '无效时间';
    } catch (e) {
      return '解析错误';
    }
  }

  // 获取焊接状态文本
  String _getWeldingStatusText(int statusValue) {
    switch (statusValue) {
      case 0:
        return '待机';
      case 1:
        return '准备中';
      case 2:
        return '卷边阶段';
      case 3:
        return '吸热阶段';
      case 4:
        return '转换阶段';
      case 5:
        return '增压阶段';
      case 6:
        return '冷却阶段';
      case 7:
        return '焊接完成';
      case 8:
        return '焊接失败';
      case 9:
        return '设备故障';
      default:
        return '未知状态($statusValue)';
    }
  }

  // 获取状态描述
  String _getStatusDescription(int statusValue) {
    switch (statusValue) {
      case 0:
        return '设备处于待机状态，等待操作指令';
      case 1:
        return '设备正在准备焊接，检查各项参数';
      case 2:
        return '正在进行管材端面卷边处理';
      case 3:
        return '热板加热，管材端面吸热软化';
      case 4:
        return '移除热板，准备对接';
      case 5:
        return '增加压力，确保焊接质量';
      case 6:
        return '冷却阶段，等待焊缝固化';
      case 7:
        return '焊接过程成功完成';
      case 8:
        return '焊接过程中出现问题，需要检查';
      case 9:
        return '设备出现故障，需要维修';
      default:
        return '状态码异常，请检查设备';
    }
  }

  // 手动同步离线数据
  Future<void> _syncOfflineData() async {
    try {
      setState(() {
        _statusMessage = '正在同步离线数据...';
      });

      final result = await _offlineModeService.manualSyncData();

      setState(() {
        _statusMessage = result ? '离线数据同步成功' : '网络连接不可用，无法同步';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '离线数据同步成功' : '网络连接不可用，无法同步'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _statusMessage = '同步离线数据失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步离线数据失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 测试GPS定位功能
  Future<void> _testGPSLocation() async {
    setState(() {
      _isLoadingLocation = true;
      _statusMessage = '正在测试GPS定位...';
      _locationData = '';
    });

    try {
      print('开始测试GPS定位功能...');

      // 使用LocationService获取位置
      final locationService = LocationService();

      // 首先检查权限状态
      String permissionStatus =
          await locationService.getLocationPermissionStatus();
      print('权限状态: $permissionStatus');

      // 尝试获取位置信息
      final location = await locationService.getLocationWithFallback();

      if (location != null) {
        String formattedData = locationService.formatLocationData(location);
        setState(() {
          _statusMessage = 'GPS定位成功，正在发送到PLC...';
        });

        print('GPS定位成功: ${location.toString()}');

        // GPS定位成功后，自动发送到PLC
        final jointNumberService = WeldingJointNumberService();
        bool sendSuccess =
            await jointNumberService.writeLocationToWeldingMachine();

        setState(() {
          _isLoadingLocation = false;
          _locationData = formattedData;
          if (sendSuccess) {
            _statusMessage = 'GPS定位成功并已发送到PLC！';
          } else {
            _statusMessage = 'GPS定位成功，但发送到PLC失败';
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(sendSuccess
                ? 'GPS定位并发送成功！经度: ${location.longitude.toStringAsFixed(6)}, 纬度: ${location.latitude.toStringAsFixed(6)}'
                : 'GPS定位成功但发送到PLC失败'),
            backgroundColor: sendSuccess ? Colors.green : Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      } else {
        setState(() {
          _isLoadingLocation = false;
          _statusMessage = 'GPS定位失败，请检查权限和GPS设置';
          _locationData =
              '定位失败\n\n可能原因：\n1. GPS权限未授予\n2. GPS服务未开启\n3. 信号较弱（建议到室外开阔区域）\n4. 网络问题\n\n权限状态: $permissionStatus';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('GPS定位失败，请检查权限和GPS设置'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );

        print('GPS定位失败');
      }
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
        _statusMessage = 'GPS定位测试异常: $e';
        _locationData = '定位异常: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('GPS定位测试异常: $e'),
          backgroundColor: Colors.red,
        ),
      );

      print('GPS定位测试异常: $e');
    }
  }

  // 构建解析后的数据显示界面
  Widget _buildParsedDataDisplay() {
    if (_parsedWeldingData == null) return Container();

    // 检查是否有解析错误
    if (_parsedWeldingData!.containsKey('error')) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error, color: Colors.red, size: 16),
                SizedBox(width: 4),
                Text(
                  '数据解析错误',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              '错误: ${_parsedWeldingData!['error']}',
              style: TextStyle(color: Colors.red[600]),
            ),
            Text(
              '详情: ${_parsedWeldingData!['message']}',
              style: TextStyle(color: Colors.red[600]),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 数据信息卡片
        _buildDataInfoCard(),
        SizedBox(height: 12),

        // 基础参数卡片
        _buildBasicParamsCard(),
        SizedBox(height: 12),

        // 工艺参数卡片
        _buildProcessParamsCard(),
        SizedBox(height: 12),

        // 状态信息卡片
        _buildStatusInfoCard(),
        SizedBox(height: 12),

        // 时间信息卡片
        _buildTimeInfoCard(),
        SizedBox(height: 12),

        // 原始数据预览卡片
        _buildRawDataCard(),
      ],
    );
  }

  // 构建数据信息卡片
  Widget _buildDataInfoCard() {
    final dataInfo = _parsedWeldingData!['dataInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue, size: 16),
              SizedBox(width: 4),
              Text(
                '数据信息',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...dataInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(color: Colors.blue[800]),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建基础参数卡片
  Widget _buildBasicParamsCard() {
    final basicParams =
        _parsedWeldingData!['basicParams'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings, color: Colors.green, size: 16),
              SizedBox(width: 4),
              Text(
                '基础参数 (VW2530-VW2540)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...basicParams.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.green[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.green[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建工艺参数卡片
  Widget _buildProcessParamsCard() {
    final processParams =
        _parsedWeldingData!['processParams'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.precision_manufacturing,
                  color: Colors.orange, size: 16),
              SizedBox(width: 4),
              Text(
                '工艺参数 (VW2544-VW2568)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...processParams.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.orange[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.orange[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建状态信息卡片
  Widget _buildStatusInfoCard() {
    final statusInfo =
        _parsedWeldingData!['statusInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.purple, size: 16),
              SizedBox(width: 4),
              Text(
                '状态信息 (VW2570)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...statusInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.purple[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.purple[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建时间信息卡片
  Widget _buildTimeInfoCard() {
    final timeInfo = _parsedWeldingData!['timeInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.teal[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.teal, size: 16),
              SizedBox(width: 4),
              Text(
                '时间信息',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...timeInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.teal[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.teal[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建原始数据预览卡片
  Widget _buildRawDataCard() {
    final rawDataPreview = _parsedWeldingData!['rawDataPreview'] as String;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.code, color: Colors.grey[600], size: 16),
              SizedBox(width: 4),
              Text(
                '原始数据预览 (前20字节)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              rawDataPreview,
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: Colors.grey[800],
              ),
            ),
          ),
          SizedBox(height: 8),
          ExpansionTile(
            title: Text(
              '查看完整原始数据',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    _weldingData,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 10,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接配置'),
      ),
      body: _isLoading ? _buildLoadingView() : _buildConfigView(),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(_statusMessage),
        ],
      ),
    );
  }

  Widget _buildConfigView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 离线模式状态卡片
          Card(
            color: _offlineState.isOfflineMode
                ? Colors.orange.shade50
                : Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _offlineState.isOfflineMode
                            ? Icons.wifi_off
                            : Icons.wifi,
                        color: _offlineState.isOfflineMode
                            ? Colors.orange
                            : Colors.green,
                      ),
                      SizedBox(width: 8),
                      Text(
                        _offlineState.isOfflineMode ? '离线模式' : '在线模式',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: _offlineState.isOfflineMode
                              ? Colors.orange[700]
                              : Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    _offlineState.isOfflineMode
                        ? '网络连接不可用，数据将保存到本地'
                        : '网络连接正常，数据将实时上传',
                    style: TextStyle(fontSize: 14),
                  ),
                  if (_offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 8),
                    Text(
                      '待上传数据: ${_offlineState.pendingUploads} 条',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  if (_offlineState.lastSyncTime != null) ...[
                    SizedBox(height: 8),
                    Text(
                      '上次同步时间: ${_formatDateTime(_offlineState.lastSyncTime!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  if (_offlineState.isOfflineMode &&
                      _offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _syncOfflineData,
                      icon: Icon(Icons.sync, size: 16),
                      label: Text('手动同步'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 状态消息卡片
          if (_statusMessage.isNotEmpty) ...[
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 12),
                    Expanded(child: Text(_statusMessage)),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          // 设备信息卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '设备信息',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      TextButton.icon(
                        onPressed:
                            _isLoadingDeviceInfo ? null : _loadDeviceInfo,
                        icon: Icon(
                          _isLoadingDeviceInfo
                              ? Icons.hourglass_top
                              : Icons.refresh,
                          size: 16,
                        ),
                        label: Text(_isLoadingDeviceInfo ? '读取中...' : '刷新'),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingDeviceInfo) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在读取设备信息...'),
                        ],
                      ),
                    ),
                  ] else ...[
                    _buildInfoRow(
                        '连接状态', _deviceInfo['connectionStatus'] ?? '未知'),
                    _buildInfoRow('焊机编号', _deviceInfo['machineNumber'] ?? '未知'),
                    _buildInfoRow(
                        '焊接标准', _deviceInfo['weldingStandard'] ?? '未知'),
                    _buildInfoRow('焊机机型', _deviceInfo['machineType'] ?? '未知'),
                    _buildInfoRow('油缸面积', _deviceInfo['cylinderArea'] ?? '未知'),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 焊机数据卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '焊机数据 (160字节)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed:
                            _isLoadingWeldingData ? null : _loadWeldingData,
                        icon: Icon(
                          _isLoadingWeldingData
                              ? Icons.hourglass_top
                              : Icons.download,
                          size: 16,
                        ),
                        label: Text(_isLoadingWeldingData ? '获取中...' : '获取数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _testParseData,
                        icon: Icon(Icons.science, size: 16),
                        label: Text('测试解析'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingWeldingData) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取160字节焊机数据...'),
                        ],
                      ),
                    ),
                  ] else if (_weldingData.isNotEmpty) ...[
                    // 显示解析后的数据
                    if (_parsedWeldingData != null) ...[
                      _buildParsedDataDisplay(),
                    ] else ...[
                      // 如果解析失败，显示原始数据
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.warning,
                                    color: Colors.orange, size: 16),
                                SizedBox(width: 4),
                                Text(
                                  '原始数据 (解析失败)',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[700],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Container(
                              constraints: BoxConstraints(maxHeight: 200),
                              child: SingleChildScrollView(
                                child: Text(
                                  _weldingData,
                                  style: TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.info_outline,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击上方按钮获取160字节焊机数据',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // GPS定位测试卡片
          Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.green),
                          SizedBox(width: 8),
                          Text(
                            'GPS定位测试',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      ElevatedButton.icon(
                        onPressed: _isLoadingLocation ? null : _testGPSLocation,
                        icon: Icon(
                          _isLoadingLocation
                              ? Icons.hourglass_top
                              : Icons.my_location,
                          size: 16,
                        ),
                        label: Text(_isLoadingLocation ? '定位中...' : '测试定位'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingLocation) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取GPS位置信息...'),
                          SizedBox(height: 4),
                          Text(
                            '请确保GPS已开启且在室外开阔区域',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else if (_locationData.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 4),
                              Text(
                                'GPS定位成功',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Text(
                            _locationData,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.location_off,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击测试定位按钮获取GPS坐标',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '提示：在室外开阔区域定位效果更好',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 如果没有加载到当前项目，显示提示卡片
          if (_currentProject == null) ...[
            Card(
              color: Colors.amber.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: const [
                        Icon(Icons.warning_amber_rounded, color: Colors.orange),
                        SizedBox(width: 8),
                        Text(
                          '未选择项目',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    const Text(
                      '您需要先在"项目管理"标签页中选择一个项目，然后设置为当前项目。\n\n'
                      '操作步骤：\n'
                      '1. 返回首页，切换到"项目管理"标签\n'
                      '2. 点击项目查看详情\n'
                      '3. 点击"设为当前项目"按钮\n'
                      '4. 返回"焊接作业"标签，重新点击"发送项目信息"',
                    ),
                    SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        icon: Icon(Icons.refresh),
                        label: Text('刷新项目信息'),
                        onPressed: _loadCurrentProject,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // 项目信息卡片 - 仅当有项目时显示
          if (_currentProject != null) ...[
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '项目信息',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildInfoRow('项目名称', _currentProject!.name),
                    _buildInfoRow('项目编号', _currentProject!.code),
                    _buildInfoRow('项目地点', _currentProject!.address),
                    _buildInfoRow('项目ID', _currentProject!.id),
                    _buildInfoRow('施工单位', _currentProject!.constructionUnit),
                  ],
                ),
              ),
            ),

            SizedBox(height: 24),

            // 操作按钮 - 仅当有项目时显示
            Center(
              child: ElevatedButton.icon(
                onPressed: _isSending
                    ? null
                    : () => _sendProjectInfoToWeldingMachine(),
                icon: Icon(_isSending ? Icons.hourglass_top : Icons.send),
                label: Text(_isSending ? '发送中...' : '发送项目信息到焊机'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label + ':',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
