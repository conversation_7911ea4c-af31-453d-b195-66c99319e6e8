import 'package:flutter/material.dart';
import '../services/bluetooth_service.dart';
import '../services/project_service.dart';
import '../services/command_service.dart';
import '../services/offline_mode_service.dart';
import '../services/location_service.dart';
import '../services/welding_joint_number_service.dart';
import '../models/project_model.dart';
import '../models/offline_data_model.dart';
import 'dart:async';

class WeldingConfigScreen extends StatefulWidget {
  @override
  _WeldingConfigScreenState createState() => _WeldingConfigScreenState();
}

class _WeldingConfigScreenState extends State<WeldingConfigScreen> {
  final BleService _bleService = BleService();
  final ProjectService _projectService = ProjectService();
  final CommandService _commandService = CommandService();
  final OfflineModeService _offlineModeService = OfflineModeService();

  Project? _currentProject;
  bool _isLoading = true;
  bool _isSending = false;
  String _machineNumber = '';
  String _statusMessage = '';

  // 添加设备信息相关变量
  Map<String, String> _deviceInfo = {
    'connectionStatus': '未知',
    'machineNumber': '未知',
    'weldingStandard': '未知',
    'machineType': '未知',
    'cylinderArea': '未知',
  };
  bool _isLoadingDeviceInfo = false;

  // 添加焊机数据相关变量
  String _weldingData = '';
  bool _isLoadingWeldingData = false;

  // 添加GPS定位相关变量
  String _locationData = '';
  bool _isLoadingLocation = false;

  // 离线模式相关变量
  OfflineModeState _offlineState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );

  StreamSubscription? _receivedDataSubscription;
  StreamSubscription? _offlineStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadCurrentProject();
    _checkConnectionAndSendInitialCommand();

    // 延迟读取设备信息，确保蓝牙连接稳定
    if (_bleService.isConnected) {
      // 延迟3秒后读取设备信息，让连接充分稳定
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _bleService.isConnected) {
          _loadDeviceInfo();
        }
      });
    }

    _receivedDataSubscription = _bleService.receivedDataStream.listen((data) {
      if (data.startsWith('【重要】焊机编号:') ||
          data.startsWith('焊机编号:') ||
          data.startsWith('【重要】设备焊机编号(ASCII文本):')) {
        String machineNumber = data.split(':').last.trim();
        setState(() {
          _machineNumber = machineNumber;
          _deviceInfo['machineNumber'] = machineNumber;
          _statusMessage = '已获取到焊机编号: $machineNumber';
        });
      } else if (data.startsWith('【重要】焊接标准:') || data.startsWith('焊接标准:')) {
        String standard = data.split(':').last.trim();
        setState(() {
          _deviceInfo['weldingStandard'] = standard;
          _statusMessage = '已获取到焊接标准';
          print('焊接标准已更新: $standard'); // 调试用
        });
      } else if (data.startsWith('【重要】焊机机型:') || data.startsWith('焊机机型:')) {
        String type = data.split(':').last.trim();
        setState(() {
          _deviceInfo['machineType'] = type;
          print('焊机机型已更新: $type'); // 调试用
        });
      } else if (data.startsWith('【重要】油缸面积:') || data.startsWith('油缸面积:')) {
        String area = data.split(':').last.trim();
        setState(() {
          _deviceInfo['cylinderArea'] = area;
          print('油缸面积已更新: $area'); // 调试用
        });
      } else if (data.startsWith('【重要】连接状态:') || data.startsWith('连接状态:')) {
        String status = data.split(':').last.trim();
        setState(() {
          _deviceInfo['connectionStatus'] = status;
          print('连接状态已更新: $status'); // 调试用
        });
      } else if (data.startsWith('【重要】160字节焊机数据已接收')) {
        setState(() {
          _statusMessage = '160字节焊机数据已接收';
        });
      } else if (data.startsWith('160字节焊机数据:')) {
        // 修复数据解析问题：正确提取数据内容并去除前后空白字符
        String weldingDataContent = data.substring('160字节焊机数据:'.length).trim();
        setState(() {
          _weldingData = weldingDataContent;
          _isLoadingWeldingData = false;
          _statusMessage = '160字节焊机数据获取成功';
        });
        print(
            '160字节焊机数据已更新: ${weldingDataContent.length > 50 ? weldingDataContent.substring(0, 50) + '...' : weldingDataContent}'); // 调试用，只显示前50个字符
      }
    });
  }

  // 初始化服务
  Future<void> _initializeServices() async {
    try {
      await _offlineModeService.initialize();

      // 获取当前状态
      setState(() {
        _offlineState = _offlineModeService.currentState;
      });

      // 监听离线模式状态变化
      _offlineStateSubscription =
          _offlineModeService.stateStream.listen((state) {
        setState(() {
          _offlineState = state;
        });
      });

      // 设置当前项目和用户（这里需要根据实际情况获取用户ID）
      if (_currentProject != null) {
        await _offlineModeService.setCurrentUserAndProject(
          'user_001', // 这里应该从用户登录状态获取
          _currentProject!.id,
        );
      }
    } catch (e) {
      print('初始化离线模式服务失败: $e');
    }
  }

  @override
  void dispose() {
    _receivedDataSubscription?.cancel();
    _offlineStateSubscription?.cancel();
    super.dispose();
  }

  // 加载当前项目
  Future<void> _loadCurrentProject() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载项目信息...';
    });

    try {
      final currentProject = await _projectService.getCurrentProject();
      setState(() {
        _currentProject = currentProject;
        _isLoading = false;
        _statusMessage = currentProject != null
            ? '项目信息已加载: ${currentProject.name}'
            : '未找到当前项目';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = '加载项目信息失败: $e';
      });
    }
  }

  // 检查蓝牙连接并发送初始命令
  Future<void> _checkConnectionAndSendInitialCommand() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，请先连接焊机';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在与焊机通信...';
      _isSending = true;
    });

    try {
      // 发送初始命令 01 06 00 03 00 01 B8 0A
      final initialCommand = _commandService.buildInitialCommand();
      final initialResult = await _bleService.sendData(initialCommand);

      if (!initialResult) {
        setState(() {
          _statusMessage = '发送初始命令失败';
          _isSending = false;
        });
        return;
      }

      // 等待响应
      await Future.delayed(Duration(milliseconds: 500));

      // 发送查询焊机编号命令 01 03 00 05 00 05 95 C8
      final queryCommand = _commandService.buildQueryMachineNumberCommand();
      final queryResult = await _bleService.sendData(queryCommand);

      if (!queryResult) {
        setState(() {
          _statusMessage = '查询焊机编号失败';
          _isSending = false;
        });
        return;
      }

      setState(() {
        _statusMessage = '已发送查询焊机编号命令，等待响应...';
        _isSending = false;
      });

      // 等待焊机编号响应
      await Future.delayed(Duration(seconds: 1));

      // 读取完整设备信息
      await _loadDeviceInfo();

      if (_machineNumber.isEmpty && _deviceInfo['machineNumber'] == '未知') {
        await Future.delayed(Duration(seconds: 1));
        _bleService.sendSpecificCommand();
      }
    } catch (e) {
      setState(() {
        _statusMessage = '与焊机通信失败: $e';
        _isSending = false;
      });
    }
  }

  // 发送项目信息到焊机
  Future<void> _sendProjectInfoToWeldingMachine() async {
    if (_currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('没有当前项目信息')),
      );
      return;
    }

    if (!_bleService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('蓝牙未连接，请先连接焊机')),
      );
      return;
    }

    setState(() {
      _statusMessage = '正在发送项目信息到焊机...';
      _isSending = true;
    });

    try {
      final result = await _commandService
          .sendProjectInfoToWeldingMachine(_currentProject!);

      setState(() {
        _isSending = false;
        _statusMessage = result ? '项目信息发送成功' : '项目信息发送部分失败';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '项目信息发送成功' : '项目信息发送部分失败'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _isSending = false;
        _statusMessage = '发送项目信息失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('发送项目信息失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 添加读取设备信息的方法
  Future<void> _loadDeviceInfo() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法读取设备信息';
        _deviceInfo['connectionStatus'] = '未连接';
      });
      return;
    }

    setState(() {
      _isLoadingDeviceInfo = true;
      _statusMessage = '正在读取设备信息...';
    });

    try {
      print('开始读取设备信息...');

      // 直接调用蓝牙服务的统一读取方法，避免重复发送命令
      print('从蓝牙服务读取完整设备信息...');
      final deviceInfo = await _bleService.readDeviceInfoForUI();
      print('设备信息已获取: $deviceInfo');

      setState(() {
        // 合并从蓝牙服务获取的设备信息
        _deviceInfo = deviceInfo;
        _isLoadingDeviceInfo = false;
        _statusMessage = '设备信息读取成功';

        // 更新机器编号显示
        if (deviceInfo['machineNumber'] != null &&
            deviceInfo['machineNumber'] != '未知' &&
            deviceInfo['machineNumber'] != '正在读取...' &&
            deviceInfo['machineNumber'] != '未获取到') {
          _machineNumber = deviceInfo['machineNumber']!;
        }
      });

      print('最终读取到的设备信息: $deviceInfo');
    } catch (e) {
      setState(() {
        _isLoadingDeviceInfo = false;
        _statusMessage = '读取设备信息失败: $e';
      });
      print('读取设备信息出错: $e');
    }
  }

  // 添加获取160字节焊机数据的方法
  Future<void> _loadWeldingData() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法获取焊机数据';
      });
      return;
    }

    setState(() {
      _isLoadingWeldingData = true;
      _statusMessage = '正在获取160字节焊机数据...';
      _weldingData = '';
    });

    try {
      print('开始获取160字节焊机数据...');

      // 发送160字节焊机数据查询命令
      bool result = await _bleService.sendQueryWeldingDataCommand();

      if (result) {
        print('160字节焊机数据查询命令发送成功，等待响应...');
        // 等待3秒接收数据
        await Future.delayed(Duration(seconds: 3));

        // 如果3秒后还没收到数据，设置超时状态
        if (_weldingData.isEmpty && _isLoadingWeldingData) {
          setState(() {
            _isLoadingWeldingData = false;
            _statusMessage = '获取焊机数据超时，请重试';
          });
        }
      } else {
        setState(() {
          _isLoadingWeldingData = false;
          _statusMessage = '发送焊机数据查询命令失败';
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingWeldingData = false;
        _statusMessage = '获取焊机数据失败: $e';
      });
      print('获取焊机数据出错: $e');
    }
  }

  // 手动同步离线数据
  Future<void> _syncOfflineData() async {
    try {
      setState(() {
        _statusMessage = '正在同步离线数据...';
      });

      final result = await _offlineModeService.manualSyncData();

      setState(() {
        _statusMessage = result ? '离线数据同步成功' : '网络连接不可用，无法同步';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '离线数据同步成功' : '网络连接不可用，无法同步'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _statusMessage = '同步离线数据失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步离线数据失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 测试GPS定位功能
  Future<void> _testGPSLocation() async {
    setState(() {
      _isLoadingLocation = true;
      _statusMessage = '正在测试GPS定位...';
      _locationData = '';
    });

    try {
      print('开始测试GPS定位功能...');

      // 使用LocationService获取位置
      final locationService = LocationService();

      // 首先检查权限状态
      String permissionStatus =
          await locationService.getLocationPermissionStatus();
      print('权限状态: $permissionStatus');

      // 尝试获取位置信息
      final location = await locationService.getLocationWithFallback();

      if (location != null) {
        String formattedData = locationService.formatLocationData(location);
        setState(() {
          _statusMessage = 'GPS定位成功，正在发送到PLC...';
        });

        print('GPS定位成功: ${location.toString()}');

        // GPS定位成功后，自动发送到PLC
        final jointNumberService = WeldingJointNumberService();
        bool sendSuccess =
            await jointNumberService.writeLocationToWeldingMachine();

        setState(() {
          _isLoadingLocation = false;
          _locationData = formattedData;
          if (sendSuccess) {
            _statusMessage = 'GPS定位成功并已发送到PLC！';
          } else {
            _statusMessage = 'GPS定位成功，但发送到PLC失败';
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(sendSuccess
                ? 'GPS定位并发送成功！经度: ${location.longitude.toStringAsFixed(6)}, 纬度: ${location.latitude.toStringAsFixed(6)}'
                : 'GPS定位成功但发送到PLC失败'),
            backgroundColor: sendSuccess ? Colors.green : Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      } else {
        setState(() {
          _isLoadingLocation = false;
          _statusMessage = 'GPS定位失败，请检查权限和GPS设置';
          _locationData =
              '定位失败\n\n可能原因：\n1. GPS权限未授予\n2. GPS服务未开启\n3. 信号较弱（建议到室外开阔区域）\n4. 网络问题\n\n权限状态: $permissionStatus';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('GPS定位失败，请检查权限和GPS设置'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );

        print('GPS定位失败');
      }
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
        _statusMessage = 'GPS定位测试异常: $e';
        _locationData = '定位异常: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('GPS定位测试异常: $e'),
          backgroundColor: Colors.red,
        ),
      );

      print('GPS定位测试异常: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接配置'),
      ),
      body: _isLoading ? _buildLoadingView() : _buildConfigView(),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(_statusMessage),
        ],
      ),
    );
  }

  Widget _buildConfigView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 离线模式状态卡片
          Card(
            color: _offlineState.isOfflineMode
                ? Colors.orange.shade50
                : Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _offlineState.isOfflineMode
                            ? Icons.wifi_off
                            : Icons.wifi,
                        color: _offlineState.isOfflineMode
                            ? Colors.orange
                            : Colors.green,
                      ),
                      SizedBox(width: 8),
                      Text(
                        _offlineState.isOfflineMode ? '离线模式' : '在线模式',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: _offlineState.isOfflineMode
                              ? Colors.orange[700]
                              : Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    _offlineState.isOfflineMode
                        ? '网络连接不可用，数据将保存到本地'
                        : '网络连接正常，数据将实时上传',
                    style: TextStyle(fontSize: 14),
                  ),
                  if (_offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 8),
                    Text(
                      '待上传数据: ${_offlineState.pendingUploads} 条',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  if (_offlineState.lastSyncTime != null) ...[
                    SizedBox(height: 8),
                    Text(
                      '上次同步时间: ${_formatDateTime(_offlineState.lastSyncTime!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  if (_offlineState.isOfflineMode &&
                      _offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _syncOfflineData,
                      icon: Icon(Icons.sync, size: 16),
                      label: Text('手动同步'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 状态消息卡片
          if (_statusMessage.isNotEmpty) ...[
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 12),
                    Expanded(child: Text(_statusMessage)),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          // 设备信息卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '设备信息',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      TextButton.icon(
                        onPressed:
                            _isLoadingDeviceInfo ? null : _loadDeviceInfo,
                        icon: Icon(
                          _isLoadingDeviceInfo
                              ? Icons.hourglass_top
                              : Icons.refresh,
                          size: 16,
                        ),
                        label: Text(_isLoadingDeviceInfo ? '读取中...' : '刷新'),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingDeviceInfo) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在读取设备信息...'),
                        ],
                      ),
                    ),
                  ] else ...[
                    _buildInfoRow(
                        '连接状态', _deviceInfo['connectionStatus'] ?? '未知'),
                    _buildInfoRow('焊机编号', _deviceInfo['machineNumber'] ?? '未知'),
                    _buildInfoRow(
                        '焊接标准', _deviceInfo['weldingStandard'] ?? '未知'),
                    _buildInfoRow('焊机机型', _deviceInfo['machineType'] ?? '未知'),
                    _buildInfoRow('油缸面积', _deviceInfo['cylinderArea'] ?? '未知'),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 焊机数据卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '焊机数据 (160字节)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed:
                            _isLoadingWeldingData ? null : _loadWeldingData,
                        icon: Icon(
                          _isLoadingWeldingData
                              ? Icons.hourglass_top
                              : Icons.download,
                          size: 16,
                        ),
                        label: Text(_isLoadingWeldingData ? '获取中...' : '获取数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingWeldingData) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取160字节焊机数据...'),
                        ],
                      ),
                    ),
                  ] else if (_weldingData.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 4),
                              Text(
                                '焊机数据 (160字节)',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Container(
                            constraints: BoxConstraints(maxHeight: 200),
                            child: SingleChildScrollView(
                              child: Text(
                                _weldingData,
                                style: TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                  color: Colors.grey[800],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.info_outline,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击上方按钮获取160字节焊机数据',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // GPS定位测试卡片
          Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.green),
                          SizedBox(width: 8),
                          Text(
                            'GPS定位测试',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      ElevatedButton.icon(
                        onPressed: _isLoadingLocation ? null : _testGPSLocation,
                        icon: Icon(
                          _isLoadingLocation
                              ? Icons.hourglass_top
                              : Icons.my_location,
                          size: 16,
                        ),
                        label: Text(_isLoadingLocation ? '定位中...' : '测试定位'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingLocation) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取GPS位置信息...'),
                          SizedBox(height: 4),
                          Text(
                            '请确保GPS已开启且在室外开阔区域',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else if (_locationData.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 4),
                              Text(
                                'GPS定位成功',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Text(
                            _locationData,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.location_off,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击测试定位按钮获取GPS坐标',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '提示：在室外开阔区域定位效果更好',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 如果没有加载到当前项目，显示提示卡片
          if (_currentProject == null) ...[
            Card(
              color: Colors.amber.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: const [
                        Icon(Icons.warning_amber_rounded, color: Colors.orange),
                        SizedBox(width: 8),
                        Text(
                          '未选择项目',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    const Text(
                      '您需要先在"项目管理"标签页中选择一个项目，然后设置为当前项目。\n\n'
                      '操作步骤：\n'
                      '1. 返回首页，切换到"项目管理"标签\n'
                      '2. 点击项目查看详情\n'
                      '3. 点击"设为当前项目"按钮\n'
                      '4. 返回"焊接作业"标签，重新点击"发送项目信息"',
                    ),
                    SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        icon: Icon(Icons.refresh),
                        label: Text('刷新项目信息'),
                        onPressed: _loadCurrentProject,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // 项目信息卡片 - 仅当有项目时显示
          if (_currentProject != null) ...[
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '项目信息',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildInfoRow('项目名称', _currentProject!.name),
                    _buildInfoRow('项目编号', _currentProject!.code),
                    _buildInfoRow('项目地点', _currentProject!.address),
                    _buildInfoRow('项目ID', _currentProject!.id),
                    _buildInfoRow('施工单位', _currentProject!.constructionUnit),
                  ],
                ),
              ),
            ),

            SizedBox(height: 24),

            // 操作按钮 - 仅当有项目时显示
            Center(
              child: ElevatedButton.icon(
                onPressed: _isSending
                    ? null
                    : () => _sendProjectInfoToWeldingMachine(),
                icon: Icon(_isSending ? Icons.hourglass_top : Icons.send),
                label: Text(_isSending ? '发送中...' : '发送项目信息到焊机'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label + ':',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
