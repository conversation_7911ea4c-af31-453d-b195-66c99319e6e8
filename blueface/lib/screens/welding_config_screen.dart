import 'package:flutter/material.dart';
import '../services/bluetooth_service.dart';
import '../services/project_service.dart';
import '../services/command_service.dart';
import '../services/offline_mode_service.dart';
import '../services/location_service.dart';
import '../services/welding_joint_number_service.dart';
import '../models/project_model.dart';
import '../models/offline_data_model.dart';
import 'dart:async';

class WeldingConfigScreen extends StatefulWidget {
  @override
  _WeldingConfigScreenState createState() => _WeldingConfigScreenState();
}

class _WeldingConfigScreenState extends State<WeldingConfigScreen> {
  final BleService _bleService = BleService();
  final ProjectService _projectService = ProjectService();
  final CommandService _commandService = CommandService();
  final OfflineModeService _offlineModeService = OfflineModeService();

  Project? _currentProject;
  bool _isLoading = true;
  bool _isSending = false;
  String _machineNumber = '';
  String _statusMessage = '';

  // 添加设备信息相关变量
  Map<String, String> _deviceInfo = {
    'connectionStatus': '未知',
    'machineNumber': '未知',
    'weldingStandard': '未知',
    'machineType': '未知',
    'cylinderArea': '未知',
  };
  bool _isLoadingDeviceInfo = false;

  // 添加焊机数据相关变量
  String _weldingData = '';
  bool _isLoadingWeldingData = false;
  Map<String, dynamic>? _parsedWeldingData;

  // 添加GPS定位相关变量
  String _locationData = '';
  bool _isLoadingLocation = false;

  // 离线模式相关变量
  OfflineModeState _offlineState = OfflineModeState(
    isOfflineMode: false,
    pendingUploads: 0,
  );

  StreamSubscription? _receivedDataSubscription;
  StreamSubscription? _offlineStateSubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _loadCurrentProject();
    _checkConnectionAndSendInitialCommand();

    // 延迟读取设备信息，确保蓝牙连接稳定
    if (_bleService.isConnected) {
      // 延迟3秒后读取设备信息，让连接充分稳定
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _bleService.isConnected) {
          _loadDeviceInfo();
        }
      });
    }

    _receivedDataSubscription = _bleService.receivedDataStream.listen((data) {
      if (data.startsWith('【重要】焊机编号:') ||
          data.startsWith('焊机编号:') ||
          data.startsWith('【重要】设备焊机编号(ASCII文本):')) {
        String machineNumber = data.split(':').last.trim();
        setState(() {
          _machineNumber = machineNumber;
          _deviceInfo['machineNumber'] = machineNumber;
          _statusMessage = '已获取到焊机编号: $machineNumber';
        });
      } else if (data.startsWith('【重要】焊接标准:') || data.startsWith('焊接标准:')) {
        String standard = data.split(':').last.trim();
        setState(() {
          _deviceInfo['weldingStandard'] = standard;
          _statusMessage = '已获取到焊接标准';
          print('焊接标准已更新: $standard'); // 调试用
        });
      } else if (data.startsWith('【重要】焊机机型:') || data.startsWith('焊机机型:')) {
        String type = data.split(':').last.trim();
        setState(() {
          _deviceInfo['machineType'] = type;
          print('焊机机型已更新: $type'); // 调试用
        });
      } else if (data.startsWith('【重要】油缸面积:') || data.startsWith('油缸面积:')) {
        String area = data.split(':').last.trim();
        setState(() {
          _deviceInfo['cylinderArea'] = area;
          print('油缸面积已更新: $area'); // 调试用
        });
      } else if (data.startsWith('【重要】连接状态:') || data.startsWith('连接状态:')) {
        String status = data.split(':').last.trim();
        setState(() {
          _deviceInfo['connectionStatus'] = status;
          print('连接状态已更新: $status'); // 调试用
        });
      } else if (data.startsWith('【重要】160字节焊机数据已接收')) {
        setState(() {
          _statusMessage = '160字节焊机数据已接收';
        });
      } else if (data.startsWith('160字节焊机数据:')) {
        // 修复数据解析问题：正确提取数据内容并去除前后空白字符
        String weldingDataContent = data.substring('160字节焊机数据:'.length).trim();
        setState(() {
          _weldingData = weldingDataContent;
          _parsedWeldingData = _parseWeldingData(weldingDataContent);
          _isLoadingWeldingData = false;
          _statusMessage = '160字节焊机数据获取成功';
        });
        print(
            '160字节焊机数据已更新: ${weldingDataContent.length > 50 ? weldingDataContent.substring(0, 50) + '...' : weldingDataContent}'); // 调试用，只显示前50个字符
      }
    });
  }

  // 初始化服务
  Future<void> _initializeServices() async {
    try {
      await _offlineModeService.initialize();

      // 获取当前状态
      setState(() {
        _offlineState = _offlineModeService.currentState;
      });

      // 监听离线模式状态变化
      _offlineStateSubscription =
          _offlineModeService.stateStream.listen((state) {
        setState(() {
          _offlineState = state;
        });
      });

      // 设置当前项目和用户（这里需要根据实际情况获取用户ID）
      if (_currentProject != null) {
        await _offlineModeService.setCurrentUserAndProject(
          'user_001', // 这里应该从用户登录状态获取
          _currentProject!.id,
        );
      }
    } catch (e) {
      print('初始化离线模式服务失败: $e');
    }
  }

  @override
  void dispose() {
    _receivedDataSubscription?.cancel();
    _offlineStateSubscription?.cancel();
    super.dispose();
  }

  // 加载当前项目
  Future<void> _loadCurrentProject() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在加载项目信息...';
    });

    try {
      final currentProject = await _projectService.getCurrentProject();
      setState(() {
        _currentProject = currentProject;
        _isLoading = false;
        _statusMessage = currentProject != null
            ? '项目信息已加载: ${currentProject.name}'
            : '未找到当前项目';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = '加载项目信息失败: $e';
      });
    }
  }

  // 检查蓝牙连接并发送初始命令
  Future<void> _checkConnectionAndSendInitialCommand() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，请先连接焊机';
      });
      return;
    }

    setState(() {
      _statusMessage = '正在与焊机通信...';
      _isSending = true;
    });

    try {
      // 发送初始命令 01 06 00 03 00 01 B8 0A
      final initialCommand = _commandService.buildInitialCommand();
      final initialResult = await _bleService.sendData(initialCommand);

      if (!initialResult) {
        setState(() {
          _statusMessage = '发送初始命令失败';
          _isSending = false;
        });
        return;
      }

      // 等待响应
      await Future.delayed(Duration(milliseconds: 500));

      // 发送查询焊机编号命令 01 03 00 05 00 05 95 C8
      final queryCommand = _commandService.buildQueryMachineNumberCommand();
      final queryResult = await _bleService.sendData(queryCommand);

      if (!queryResult) {
        setState(() {
          _statusMessage = '查询焊机编号失败';
          _isSending = false;
        });
        return;
      }

      setState(() {
        _statusMessage = '已发送查询焊机编号命令，等待响应...';
        _isSending = false;
      });

      // 等待焊机编号响应
      await Future.delayed(Duration(seconds: 1));

      // 读取完整设备信息
      await _loadDeviceInfo();

      if (_machineNumber.isEmpty && _deviceInfo['machineNumber'] == '未知') {
        await Future.delayed(Duration(seconds: 1));
        _bleService.sendSpecificCommand();
      }
    } catch (e) {
      setState(() {
        _statusMessage = '与焊机通信失败: $e';
        _isSending = false;
      });
    }
  }

  // 发送项目信息到焊机
  Future<void> _sendProjectInfoToWeldingMachine() async {
    if (_currentProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('没有当前项目信息')),
      );
      return;
    }

    if (!_bleService.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('蓝牙未连接，请先连接焊机')),
      );
      return;
    }

    setState(() {
      _statusMessage = '正在发送项目信息到焊机...';
      _isSending = true;
    });

    try {
      final result = await _commandService
          .sendProjectInfoToWeldingMachine(_currentProject!);

      setState(() {
        _isSending = false;
        _statusMessage = result ? '项目信息发送成功' : '项目信息发送部分失败';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '项目信息发送成功' : '项目信息发送部分失败'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _isSending = false;
        _statusMessage = '发送项目信息失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('发送项目信息失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 添加读取设备信息的方法
  Future<void> _loadDeviceInfo() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法读取设备信息';
        _deviceInfo['connectionStatus'] = '未连接';
      });
      return;
    }

    setState(() {
      _isLoadingDeviceInfo = true;
      _statusMessage = '正在读取设备信息...';
    });

    try {
      print('开始读取设备信息...');

      // 直接调用蓝牙服务的统一读取方法，避免重复发送命令
      print('从蓝牙服务读取完整设备信息...');
      final deviceInfo = await _bleService.readDeviceInfoForUI();
      print('设备信息已获取: $deviceInfo');

      setState(() {
        // 合并从蓝牙服务获取的设备信息
        _deviceInfo = deviceInfo;
        _isLoadingDeviceInfo = false;
        _statusMessage = '设备信息读取成功';

        // 更新机器编号显示
        if (deviceInfo['machineNumber'] != null &&
            deviceInfo['machineNumber'] != '未知' &&
            deviceInfo['machineNumber'] != '正在读取...' &&
            deviceInfo['machineNumber'] != '未获取到') {
          _machineNumber = deviceInfo['machineNumber']!;
        }
      });

      print('最终读取到的设备信息: $deviceInfo');
    } catch (e) {
      setState(() {
        _isLoadingDeviceInfo = false;
        _statusMessage = '读取设备信息失败: $e';
      });
      print('读取设备信息出错: $e');
    }
  }

  // 添加获取160字节焊机数据的方法
  Future<void> _loadWeldingData() async {
    if (!_bleService.isConnected) {
      setState(() {
        _statusMessage = '蓝牙未连接，无法获取焊机数据';
      });
      return;
    }

    setState(() {
      _isLoadingWeldingData = true;
      _statusMessage = '正在获取160字节焊机数据...';
      _weldingData = '';
    });

    try {
      print('开始获取160字节焊机数据...');

      // 首先尝试从缓存获取数据
      final deviceInfo = await _bleService.readDeviceInfoForUI();
      if (deviceInfo.containsKey('weldingData') &&
          deviceInfo['weldingData'] != null) {
        String cachedData = deviceInfo['weldingData'].toString();
        if (cachedData.isNotEmpty && cachedData != '读取中') {
          setState(() {
            _weldingData = cachedData;
            _parsedWeldingData = _parseWeldingData(cachedData);
            _isLoadingWeldingData = false;
            _statusMessage = '从缓存获取160字节焊机数据成功';
          });
          print(
              '从缓存获取到160字节数据: ${cachedData.length > 50 ? '${cachedData.substring(0, 50)}...' : cachedData}');
          return;
        }
      }

      // 发送160字节焊机数据查询命令
      bool result = await _bleService.sendQueryWeldingDataCommand();

      if (result) {
        print('160字节焊机数据查询命令发送成功，等待响应...');
        // 等待3秒接收数据
        await Future.delayed(Duration(seconds: 3));

        // 再次检查缓存，可能数据已经更新
        final updatedDeviceInfo = await _bleService.readDeviceInfoForUI();
        if (updatedDeviceInfo.containsKey('weldingData') &&
            updatedDeviceInfo['weldingData'] != null) {
          String updatedData = updatedDeviceInfo['weldingData'].toString();
          if (updatedData.isNotEmpty && updatedData != '读取中') {
            setState(() {
              _weldingData = updatedData;
              _parsedWeldingData = _parseWeldingData(updatedData);
              _isLoadingWeldingData = false;
              _statusMessage = '160字节焊机数据获取成功';
            });
            print(
                '获取到更新的160字节数据: ${updatedData.length > 50 ? '${updatedData.substring(0, 50)}...' : updatedData}');
            return;
          }
        }

        // 如果3秒后还没收到数据，设置超时状态
        if (_weldingData.isEmpty && _isLoadingWeldingData) {
          setState(() {
            _isLoadingWeldingData = false;
            _statusMessage = '获取焊机数据超时，请重试';
          });
        }
      } else {
        setState(() {
          _isLoadingWeldingData = false;
          _statusMessage = '发送焊机数据查询命令失败';
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingWeldingData = false;
        _statusMessage = '获取焊机数据失败: $e';
      });
      print('获取焊机数据出错: $e');
    }
  }

  // 测试数据解析功能
  void _testParseData() {
    // 模拟焊机实际返回的测试数据格式
    // 前34个字节为0，然后是测试数据：1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0...
    List<int> testBytes = List.filled(160, 0);

    // 从字节索引34开始填入测试数据（小端序格式）
    for (int i = 0; i < 63; i++) {
      int value = (i % 9) + 1; // 循环1-9
      testBytes[34 + i * 2] = value; // 低字节（小端序）
      testBytes[34 + i * 2 + 1] = 0; // 高字节
    }

    // 转换为十六进制字符串
    String testData = testBytes
        .map((b) => b.toRadixString(16).padLeft(2, '0').toUpperCase())
        .join('');

    setState(() {
      _weldingData = testData;
      _parsedWeldingData = _parseWeldingData(testData);
      _statusMessage = '测试数据解析完成 - 模拟焊机实际数据格式';
    });

    print('测试数据前40字节: ${testBytes.take(40).join(',')}');
  }

  // 解析160字节焊机数据
  Map<String, dynamic> _parseWeldingData(String hexData) {
    try {
      // 移除空格并转换为字节数组
      String cleanHex = hexData.replaceAll(' ', '').toUpperCase();

      if (cleanHex.length != 320) {
        return {
          'error': '数据长度错误',
          'message': '期望320字符(160字节)，实际${cleanHex.length}字符',
          'rawData': hexData,
        };
      }

      List<int> bytes = [];
      for (int i = 0; i < cleanHex.length; i += 2) {
        bytes.add(int.parse(cleanHex.substring(i, i + 2), radix: 16));
      }

      if (bytes.length != 160) {
        return {
          'error': '字节数量错误',
          'message': '期望160字节，实际${bytes.length}字节',
          'rawData': hexData,
        };
      }

      // 解析各个参数
      Map<String, dynamic> parsed = {
        'dataInfo': {
          '数据长度': '${bytes.length} 字节',
          '数据格式': '十六进制',
          '解析时间': DateTime.now().toString().substring(0, 19),
        },
        'basicParams': _parseBasicParams(bytes),
        'processParams': _parseProcessParams(bytes),
        'statusInfo': _parseStatusInfo(bytes),
        'timeInfo': _parseTimeInfo(bytes),
        'rawDataPreview': bytes
            .take(20)
            .map((b) => b.toRadixString(16).padLeft(2, '0').toUpperCase())
            .join(' '),
      };

      return parsed;
    } catch (e) {
      return {
        'error': '解析失败',
        'message': e.toString(),
        'rawData': hexData,
      };
    }
  }

  // 解析基础参数 (VW2530-VW2540)
  Map<String, dynamic> _parseBasicParams(List<int> bytes) {
    // 根据实际数据分析，测试数据从字节索引34开始
    // 1,0,2,0,3,0,4,0... 对应管材直径=1, SDR=2, 厚度=3, 环境温度=4
    int dataStartIndex = 17; // 从字索引17开始 (字节索引34)

    return {
      '管材直径': '${_getWordValue(bytes, dataStartIndex + 0)} mm', // 字节34-35: 1,0
      '管材SDR':
          _getWordValue(bytes, dataStartIndex + 1).toString(), // 字节36-37: 2,0
      '管材厚度': '${_getWordValue(bytes, dataStartIndex + 2)} mm', // 字节38-39: 3,0
      '环境温度': '${_getWordValue(bytes, dataStartIndex + 3)} °C', // 字节40-41: 4,0
      '热板温度': '${_getWordValue(bytes, dataStartIndex + 4)} °C', // 字节42-43: 5,0
      '拖动压力': '${_getWordValue(bytes, dataStartIndex + 5)} bar', // 字节44-45: 6,0
    };
  }

  // 解析工艺参数 (VW2544-VW2568)
  Map<String, dynamic> _parseProcessParams(List<int> bytes) {
    // 继续从基础参数后面读取，基础参数占用6个字，工艺参数从字索引23开始
    int dataStartIndex = 17; // 基础参数起始位置
    int processStartIndex = dataStartIndex + 6; // 工艺参数起始位置 (字索引23)

    return {
      '卷边设定压力':
          '${_getWordValue(bytes, processStartIndex + 0)} bar', // 字节46-47: 7,0
      '卷边实际压力':
          '${_getWordValue(bytes, processStartIndex + 1)} bar', // 字节48-49: 8,0
      '卷边设定时间':
          '${_getWordValue(bytes, processStartIndex + 2)} s', // 字节50-51: 9,0
      '卷边实际时间':
          '${_getWordValue(bytes, processStartIndex + 3)} s', // 字节52-53: 1,0
      '吸热设定压力':
          '${_getWordValue(bytes, processStartIndex + 4)} bar', // 字节54-55: 2,0
      '吸热实际压力':
          '${_getWordValue(bytes, processStartIndex + 5)} bar', // 字节56-57: 3,0
      '吸热设定时间':
          '${_getWordValue(bytes, processStartIndex + 6)} s', // 字节58-59: 4,0
      '吸热实际时间':
          '${_getWordValue(bytes, processStartIndex + 7)} s', // 字节60-61: 5,0
      '转换时间':
          '${_getWordValue(bytes, processStartIndex + 8)} s', // 字节62-63: 6,0
      '增压时间':
          '${_getWordValue(bytes, processStartIndex + 9)} s', // 字节64-65: 7,0
      '冷却设定压力':
          '${_getWordValue(bytes, processStartIndex + 10)} bar', // 字节66-67: 8,0
      '冷却实际压力':
          '${_getWordValue(bytes, processStartIndex + 11)} bar', // 字节68-69: 9,0
      '冷却时间':
          '${_getWordValue(bytes, processStartIndex + 12)} s', // 字节70-71: 1,0
    };
  }

  // 解析状态信息 (VW2570)
  Map<String, dynamic> _parseStatusInfo(List<int> bytes) {
    int statusValue = _getWordValue(bytes, 20);
    return {
      '焊接状态码': statusValue,
      '焊接状态': _getWeldingStatusText(statusValue),
      '状态描述': _getStatusDescription(statusValue),
    };
  }

  // 解析时间信息
  Map<String, dynamic> _parseTimeInfo(List<int> bytes) {
    return {
      '熔接开始日期': _parseDate(bytes, 25, 30), // VW2580-VW2590
      '熔接开始时间': _parseTime(bytes, 35, 39), // VW2600-VW2608
      '熔接结束日期': _parseDate(bytes, 45, 50), // VW2620-VW2630
      '熔接结束时间': _parseTime(bytes, 55, 59), // VW2640-VW2648
    };
  }

  // 从字节数组获取16位字值（小端序）
  int _getWordValue(List<int> bytes, int wordIndex) {
    int byteIndex = wordIndex * 2;
    if (byteIndex + 1 < bytes.length) {
      // 小端序：低字节在前，高字节在后
      return bytes[byteIndex] | (bytes[byteIndex + 1] << 8);
    }
    return 0;
  }

  // 解析日期
  String _parseDate(List<int> bytes, int startWordIndex, int endWordIndex) {
    try {
      int year = _getWordValue(bytes, startWordIndex);
      int month = _getWordValue(bytes, startWordIndex + 1);
      int day = _getWordValue(bytes, startWordIndex + 2);

      if (year > 1900 &&
          year < 2100 &&
          month >= 1 &&
          month <= 12 &&
          day >= 1 &&
          day <= 31) {
        return '$year-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
      }
      return '无效日期';
    } catch (e) {
      return '解析错误';
    }
  }

  // 解析时间
  String _parseTime(List<int> bytes, int startWordIndex, int endWordIndex) {
    try {
      int hour = _getWordValue(bytes, startWordIndex);
      int minute = _getWordValue(bytes, startWordIndex + 1);
      int second = _getWordValue(bytes, startWordIndex + 2);

      if (hour >= 0 &&
          hour < 24 &&
          minute >= 0 &&
          minute < 60 &&
          second >= 0 &&
          second < 60) {
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')}';
      }
      return '无效时间';
    } catch (e) {
      return '解析错误';
    }
  }

  // 获取焊接状态文本
  String _getWeldingStatusText(int statusValue) {
    switch (statusValue) {
      case 0:
        return '待机';
      case 1:
        return '准备中';
      case 2:
        return '卷边阶段';
      case 3:
        return '吸热阶段';
      case 4:
        return '转换阶段';
      case 5:
        return '增压阶段';
      case 6:
        return '冷却阶段';
      case 7:
        return '焊接完成';
      case 8:
        return '焊接失败';
      case 9:
        return '设备故障';
      default:
        return '未知状态($statusValue)';
    }
  }

  // 获取状态描述
  String _getStatusDescription(int statusValue) {
    switch (statusValue) {
      case 0:
        return '设备处于待机状态，等待操作指令';
      case 1:
        return '设备正在准备焊接，检查各项参数';
      case 2:
        return '正在进行管材端面卷边处理';
      case 3:
        return '热板加热，管材端面吸热软化';
      case 4:
        return '移除热板，准备对接';
      case 5:
        return '增加压力，确保焊接质量';
      case 6:
        return '冷却阶段，等待焊缝固化';
      case 7:
        return '焊接过程成功完成';
      case 8:
        return '焊接过程中出现问题，需要检查';
      case 9:
        return '设备出现故障，需要维修';
      default:
        return '状态码异常，请检查设备';
    }
  }

  // 手动同步离线数据
  Future<void> _syncOfflineData() async {
    try {
      setState(() {
        _statusMessage = '正在同步离线数据...';
      });

      final result = await _offlineModeService.manualSyncData();

      setState(() {
        _statusMessage = result ? '离线数据同步成功' : '网络连接不可用，无法同步';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? '离线数据同步成功' : '网络连接不可用，无法同步'),
          backgroundColor: result ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      setState(() {
        _statusMessage = '同步离线数据失败: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('同步离线数据失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 测试GPS定位功能
  Future<void> _testGPSLocation() async {
    setState(() {
      _isLoadingLocation = true;
      _statusMessage = '正在测试GPS定位...';
      _locationData = '';
    });

    try {
      print('开始测试GPS定位功能...');

      // 使用LocationService获取位置
      final locationService = LocationService();

      // 首先检查权限状态
      String permissionStatus =
          await locationService.getLocationPermissionStatus();
      print('权限状态: $permissionStatus');

      // 尝试获取位置信息
      final location = await locationService.getLocationWithFallback();

      if (location != null) {
        String formattedData = locationService.formatLocationData(location);
        setState(() {
          _statusMessage = 'GPS定位成功，正在发送到PLC...';
        });

        print('GPS定位成功: ${location.toString()}');

        // GPS定位成功后，自动发送到PLC
        final jointNumberService = WeldingJointNumberService();
        bool sendSuccess =
            await jointNumberService.writeLocationToWeldingMachine();

        setState(() {
          _isLoadingLocation = false;
          _locationData = formattedData;
          if (sendSuccess) {
            _statusMessage = 'GPS定位成功并已发送到PLC！';
          } else {
            _statusMessage = 'GPS定位成功，但发送到PLC失败';
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(sendSuccess
                ? 'GPS定位并发送成功！经度: ${location.longitude.toStringAsFixed(6)}, 纬度: ${location.latitude.toStringAsFixed(6)}'
                : 'GPS定位成功但发送到PLC失败'),
            backgroundColor: sendSuccess ? Colors.green : Colors.orange,
            duration: Duration(seconds: 4),
          ),
        );
      } else {
        setState(() {
          _isLoadingLocation = false;
          _statusMessage = 'GPS定位失败，请检查权限和GPS设置';
          _locationData =
              '定位失败\n\n可能原因：\n1. GPS权限未授予\n2. GPS服务未开启\n3. 信号较弱（建议到室外开阔区域）\n4. 网络问题\n\n权限状态: $permissionStatus';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('GPS定位失败，请检查权限和GPS设置'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );

        print('GPS定位失败');
      }
    } catch (e) {
      setState(() {
        _isLoadingLocation = false;
        _statusMessage = 'GPS定位测试异常: $e';
        _locationData = '定位异常: $e';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('GPS定位测试异常: $e'),
          backgroundColor: Colors.red,
        ),
      );

      print('GPS定位测试异常: $e');
    }
  }

  // 构建解析后的数据显示界面
  Widget _buildParsedDataDisplay() {
    if (_parsedWeldingData == null) return Container();

    // 检查是否有解析错误
    if (_parsedWeldingData!.containsKey('error')) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error, color: Colors.red, size: 16),
                SizedBox(width: 4),
                Text(
                  '数据解析错误',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              '错误: ${_parsedWeldingData!['error']}',
              style: TextStyle(color: Colors.red[600]),
            ),
            Text(
              '详情: ${_parsedWeldingData!['message']}',
              style: TextStyle(color: Colors.red[600]),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 数据信息卡片
        _buildDataInfoCard(),
        SizedBox(height: 12),

        // 基础参数卡片
        _buildBasicParamsCard(),
        SizedBox(height: 12),

        // 工艺参数卡片
        _buildProcessParamsCard(),
        SizedBox(height: 12),

        // 状态信息卡片
        _buildStatusInfoCard(),
        SizedBox(height: 12),

        // 时间信息卡片
        _buildTimeInfoCard(),
        SizedBox(height: 12),

        // 原始数据预览卡片
        _buildRawDataCard(),
      ],
    );
  }

  // 构建数据信息卡片
  Widget _buildDataInfoCard() {
    final dataInfo = _parsedWeldingData!['dataInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue, size: 16),
              SizedBox(width: 4),
              Text(
                '数据信息',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...dataInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(color: Colors.blue[800]),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建基础参数卡片
  Widget _buildBasicParamsCard() {
    final basicParams =
        _parsedWeldingData!['basicParams'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings, color: Colors.green, size: 16),
              SizedBox(width: 4),
              Text(
                '基础参数 (VW2530-VW2540)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...basicParams.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.green[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.green[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建工艺参数卡片
  Widget _buildProcessParamsCard() {
    final processParams =
        _parsedWeldingData!['processParams'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.precision_manufacturing,
                  color: Colors.orange, size: 16),
              SizedBox(width: 4),
              Text(
                '工艺参数 (VW2544-VW2568)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...processParams.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.orange[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.orange[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建状态信息卡片
  Widget _buildStatusInfoCard() {
    final statusInfo =
        _parsedWeldingData!['statusInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.purple, size: 16),
              SizedBox(width: 4),
              Text(
                '状态信息 (VW2570)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...statusInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.purple[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.purple[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建时间信息卡片
  Widget _buildTimeInfoCard() {
    final timeInfo = _parsedWeldingData!['timeInfo'] as Map<String, dynamic>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.teal[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.teal, size: 16),
              SizedBox(width: 4),
              Text(
                '时间信息',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          ...timeInfo.entries
              .map((entry) => Padding(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            '${entry.key}:',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.teal[600],
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            entry.value.toString(),
                            style: TextStyle(
                              color: Colors.teal[800],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  // 构建原始数据预览卡片
  Widget _buildRawDataCard() {
    final rawDataPreview = _parsedWeldingData!['rawDataPreview'] as String;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.code, color: Colors.grey[600], size: 16),
              SizedBox(width: 4),
              Text(
                '原始数据预览 (前20字节)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              rawDataPreview,
              style: TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                color: Colors.grey[800],
              ),
            ),
          ),
          SizedBox(height: 8),
          ExpansionTile(
            title: Text(
              '查看完整原始数据',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    _weldingData,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 10,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('焊接配置'),
      ),
      body: _isLoading ? _buildLoadingView() : _buildConfigView(),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(_statusMessage),
        ],
      ),
    );
  }

  Widget _buildConfigView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 离线模式状态卡片
          Card(
            color: _offlineState.isOfflineMode
                ? Colors.orange.shade50
                : Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _offlineState.isOfflineMode
                            ? Icons.wifi_off
                            : Icons.wifi,
                        color: _offlineState.isOfflineMode
                            ? Colors.orange
                            : Colors.green,
                      ),
                      SizedBox(width: 8),
                      Text(
                        _offlineState.isOfflineMode ? '离线模式' : '在线模式',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: _offlineState.isOfflineMode
                              ? Colors.orange[700]
                              : Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    _offlineState.isOfflineMode
                        ? '网络连接不可用，数据将保存到本地'
                        : '网络连接正常，数据将实时上传',
                    style: TextStyle(fontSize: 14),
                  ),
                  if (_offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 8),
                    Text(
                      '待上传数据: ${_offlineState.pendingUploads} 条',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  if (_offlineState.lastSyncTime != null) ...[
                    SizedBox(height: 8),
                    Text(
                      '上次同步时间: ${_formatDateTime(_offlineState.lastSyncTime!)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                  if (_offlineState.isOfflineMode &&
                      _offlineState.pendingUploads > 0) ...[
                    SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: _syncOfflineData,
                      icon: Icon(Icons.sync, size: 16),
                      label: Text('手动同步'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 状态消息卡片
          if (_statusMessage.isNotEmpty) ...[
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    SizedBox(width: 12),
                    Expanded(child: Text(_statusMessage)),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],

          // 设备信息卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '设备信息',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      TextButton.icon(
                        onPressed:
                            _isLoadingDeviceInfo ? null : _loadDeviceInfo,
                        icon: Icon(
                          _isLoadingDeviceInfo
                              ? Icons.hourglass_top
                              : Icons.refresh,
                          size: 16,
                        ),
                        label: Text(_isLoadingDeviceInfo ? '读取中...' : '刷新'),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingDeviceInfo) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在读取设备信息...'),
                        ],
                      ),
                    ),
                  ] else ...[
                    _buildInfoRow(
                        '连接状态', _deviceInfo['connectionStatus'] ?? '未知'),
                    _buildInfoRow('焊机编号', _deviceInfo['machineNumber'] ?? '未知'),
                    _buildInfoRow(
                        '焊接标准', _deviceInfo['weldingStandard'] ?? '未知'),
                    _buildInfoRow('焊机机型', _deviceInfo['machineType'] ?? '未知'),
                    _buildInfoRow('油缸面积', _deviceInfo['cylinderArea'] ?? '未知'),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 焊机数据卡片
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '焊机数据 (160字节)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed:
                            _isLoadingWeldingData ? null : _loadWeldingData,
                        icon: Icon(
                          _isLoadingWeldingData
                              ? Icons.hourglass_top
                              : Icons.download,
                          size: 16,
                        ),
                        label: Text(_isLoadingWeldingData ? '获取中...' : '获取数据'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _testParseData,
                        icon: Icon(Icons.science, size: 16),
                        label: Text('测试解析'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingWeldingData) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取160字节焊机数据...'),
                        ],
                      ),
                    ),
                  ] else if (_weldingData.isNotEmpty) ...[
                    // 显示解析后的数据
                    if (_parsedWeldingData != null) ...[
                      _buildParsedDataDisplay(),
                    ] else ...[
                      // 如果解析失败，显示原始数据
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.warning,
                                    color: Colors.orange, size: 16),
                                SizedBox(width: 4),
                                Text(
                                  '原始数据 (解析失败)',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange[700],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Container(
                              constraints: BoxConstraints(maxHeight: 200),
                              child: SingleChildScrollView(
                                child: Text(
                                  _weldingData,
                                  style: TextStyle(
                                    fontFamily: 'monospace',
                                    fontSize: 12,
                                    color: Colors.grey[800],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.info_outline,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击上方按钮获取160字节焊机数据',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // GPS定位测试卡片
          Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.green),
                          SizedBox(width: 8),
                          Text(
                            'GPS定位测试',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      ElevatedButton.icon(
                        onPressed: _isLoadingLocation ? null : _testGPSLocation,
                        icon: Icon(
                          _isLoadingLocation
                              ? Icons.hourglass_top
                              : Icons.my_location,
                          size: 16,
                        ),
                        label: Text(_isLoadingLocation ? '定位中...' : '测试定位'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  if (_isLoadingLocation) ...[
                    Center(
                      child: Column(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 8),
                          Text('正在获取GPS位置信息...'),
                          SizedBox(height: 4),
                          Text(
                            '请确保GPS已开启且在室外开阔区域',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else if (_locationData.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green, size: 16),
                              SizedBox(width: 4),
                              Text(
                                'GPS定位成功',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Text(
                            _locationData,
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        children: [
                          Icon(Icons.location_off,
                              color: Colors.grey, size: 24),
                          SizedBox(height: 8),
                          Text(
                            '点击测试定位按钮获取GPS坐标',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '提示：在室外开阔区域定位效果更好',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          SizedBox(height: 16),

          // 如果没有加载到当前项目，显示提示卡片
          if (_currentProject == null) ...[
            Card(
              color: Colors.amber.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: const [
                        Icon(Icons.warning_amber_rounded, color: Colors.orange),
                        SizedBox(width: 8),
                        Text(
                          '未选择项目',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    const Text(
                      '您需要先在"项目管理"标签页中选择一个项目，然后设置为当前项目。\n\n'
                      '操作步骤：\n'
                      '1. 返回首页，切换到"项目管理"标签\n'
                      '2. 点击项目查看详情\n'
                      '3. 点击"设为当前项目"按钮\n'
                      '4. 返回"焊接作业"标签，重新点击"发送项目信息"',
                    ),
                    SizedBox(height: 12),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton.icon(
                        icon: Icon(Icons.refresh),
                        label: Text('刷新项目信息'),
                        onPressed: _loadCurrentProject,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          // 项目信息卡片 - 仅当有项目时显示
          if (_currentProject != null) ...[
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '项目信息',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 16),
                    _buildInfoRow('项目名称', _currentProject!.name),
                    _buildInfoRow('项目编号', _currentProject!.code),
                    _buildInfoRow('项目地点', _currentProject!.address),
                    _buildInfoRow('项目ID', _currentProject!.id),
                    _buildInfoRow('施工单位', _currentProject!.constructionUnit),
                  ],
                ),
              ),
            ),

            SizedBox(height: 24),

            // 操作按钮 - 仅当有项目时显示
            Center(
              child: ElevatedButton.icon(
                onPressed: _isSending
                    ? null
                    : () => _sendProjectInfoToWeldingMachine(),
                icon: Icon(_isSending ? Icons.hourglass_top : Icons.send),
                label: Text(_isSending ? '发送中...' : '发送项目信息到焊机'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label + ':',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  // 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
